export const Genders = [
  {
    value: true,
    label: "Nam",
  },
  {
    value: false,
    label: "Nữ",
  },
];

// export const sampleQuestions = [
//   {
//     value: "EduSource có những loại tài liệu nào?",
//   },
//   {
//     value: "Làm sao để mua tài liệu học tiếng Anh?",
//   },
//   {
//     value: "Tài liệu có hỗ trợ file PDF hoặc PowerPoint không?",
//   },
// ];

export const sampleQuestions = [
  {
    question: "Blindbox là gì?",
    answer:
      "Blindbox là các hộp quà bí ẩn chứa sản phẩm ngẫu nhiên bên trong. Bạn sẽ không biết món quà cụ thể cho đến khi mở hộp, tạo nên sự bất ngờ và thú vị.",
  },
  {
    question: "Trang web có những loại sản phẩm nào?",
    answer:
      "<PERSON>úng tôi cung cấp cả Blindbox và các mặt hàng không phải Blindbox như quà tặng, ph<PERSON>, <PERSON><PERSON> chơi m<PERSON> h<PERSON>nh, văn phòng phẩm, và nhiều sản phẩm độc đáo khác.",
  },
  {
    question: "Tôi có thể chọn sản phẩm cụ thể trong Blindbox không?",
    answer:
      "Không, với Blindbox bạn sẽ không biết trước sản phẩm bên trong. Đó chính là điểm hấp dẫn và bất ngờ mà nó mang lại!",
  },
  {
    question: "Có thể mua sản phẩm không phải Blindbox không?",
    answer:
      "Có, ngoài Blindbox, bạn hoàn toàn có thể mua các sản phẩm cụ thể được hiển thị rõ ràng trong danh mục sản phẩm.",
  },
  {
    question: "Làm sao để đặt hàng?",
    answer:
      "Bạn chỉ cần chọn sản phẩm, thêm vào giỏ hàng và tiến hành thanh toán theo hướng dẫn trên trang web.",
  },
  {
    question: "Tôi có thể thanh toán bằng phương thức nào?",
    answer:
      "Chúng tôi hỗ trợ thanh toán qua thẻ ngân hàng, ví điện tử Momo và chuyển khoản.",
  },
  {
    question: "Chính sách đổi trả như thế nào?",
    answer:
      "Chúng tôi hỗ trợ đổi trả đối với các sản phẩm lỗi hoặc không đúng mô tả (không áp dụng cho Blindbox đã mở). Vui lòng liên hệ bộ phận CSKH để được hỗ trợ.",
  },
];
