{"name": "capstone_fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 4040", "build": "next build", "start": "next start -p 4040", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@imgly/background-removal": "^1.6.0", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.6", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.6.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tanstack/react-query": "^5.71.10", "@tanstack/react-query-devtools": "^5.71.10", "@tippyjs/react": "^4.2.6", "apexcharts": "^4.7.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.9.1", "gsap": "^3.13.0", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.1", "lucide-react": "^0.487.0", "moment": "^2.30.1", "motion": "^12.10.1", "next": "14.2.3", "onnxruntime-web": "^1.21.0-dev.20250206-d981b153d3", "photoswipe": "^5.4.4", "react": "^18", "react-apexcharts": "^1.7.0", "react-confetti": "^6.4.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-easy-crop": "^5.4.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-photoswipe-gallery": "^3.1.1", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "sonner": "^2.0.3", "string-similarity": "^4.0.4", "swiper": "^11.2.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/string-similarity": "^4.0.2", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}