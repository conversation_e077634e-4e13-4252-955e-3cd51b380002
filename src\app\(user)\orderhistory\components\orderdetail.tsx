"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { format } from "date-fns";
import { ArrowLeft, Package, Truck, CheckCircle, Clock, MapPin } from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { OrderResponse } from "@/services/order/typings";
import useGetOrderById from "../hooks/useGetOrderById";
import { PaymentInfoStatus, PaymentInfoStatusText } from "@/const/products";

// Order Tracking Timeline Component
const OrderTrackingTimeline = ({ order }: { order: OrderResponse }) => {
  const trackingSteps = [
    {
      id: 1,
      title: "Đơn H<PERSON>ng <PERSON> Đặt",
      time: format(new Date(order.placedAt), "HH:mm dd-MM-yyyy"),
      status: "completed",
      icon: <Package className="w-5 h-5" />,
    },
    {
      id: 2,
      title: "Đơn Hàng Đã Thanh Toán",
      time: order.payment.paidAt ? format(new Date(order.payment.paidAt), "HH:mm dd-MM-yyyy") : "",
      status: order.payment.status === PaymentInfoStatus.Paid || order.payment.status === PaymentInfoStatus.Completed ? "completed" : "pending",
      icon: <CheckCircle className="w-5 h-5" />,
    },
    {
      id: 3,
      title: "Đã Giao Cho ĐVVC",
      time: "21:00 21-04-2025", // Mock data - replace with real data
      status: "pending",
      icon: <Truck className="w-5 h-5" />,
    },
    {
      id: 4,
      title: "Đã Nhận Được Hàng",
      time: order.completedAt ? format(new Date(order.completedAt), "HH:mm dd-MM-yyyy") : "",
      status: order.completedAt ? "completed" : "pending",
      icon: <CheckCircle className="w-5 h-5" />,
    },
    {
      id: 5,
      title: "Đơn Hàng Đã Hoàn Thành",
      time: order.completedAt ? format(new Date(order.completedAt), "HH:mm dd-MM-yyyy") : "",
      status: order.completedAt ? "completed" : "pending",
      icon: <CheckCircle className="w-5 h-5" />,
    },
  ];

  return (
    <Card>
      <CardContent className="p-6">
        <div className="relative">
          {trackingSteps.map((step, index) => (
            <div key={step.id} className="flex items-start mb-8 last:mb-0">
              {/* Timeline Line */}
              {index < trackingSteps.length - 1 && (
                <div className="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"></div>
              )}

              {/* Icon */}
              <div className={`flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center border-2 ${step.status === "completed"
                ? "bg-green-100 border-green-500 text-green-600"
                : "bg-gray-100 border-gray-300 text-gray-400"
                }`}>
                {step.icon}
              </div>

              {/* Content */}
              <div className="ml-4 flex-1">
                <h3 className={`font-medium ${step.status === "completed" ? "text-gray-900" : "text-gray-500"
                  }`}>
                  {step.title}
                </h3>
                {step.time && (
                  <p className="text-sm text-gray-500 mt-1">{step.time}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default function OrderDetail() {
  const { orderId } = useParams();
  const router = useRouter();
  const { getOrderDetailApi, isPending } = useGetOrderById();
  const [order, setOrder] = useState<OrderResponse | null>(null);
  const [tab, setTab] = useState("order");

  useEffect(() => {
    if (!orderId) return;
    const fetchData = async () => {
      const res = await getOrderDetailApi(orderId as string);
      if (res?.isSuccess) {
        setOrder(res.value.data);
      }
    };
    fetchData();
  }, [orderId]);

  if (isPending || !order) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-16 space-y-4">
        <Skeleton className="h-6 w-1/2" />
        <Card><CardContent className="p-4 space-y-3"><Skeleton className="h-4 w-full" /></CardContent></Card>
        <div className="space-y-2">
          {[...Array(2)].map((_, i) => (
            <Card key={i}><CardContent className="p-4"><Skeleton className="h-4 w-full" /></CardContent></Card>
          ))}
        </div>
      </div>
    );
  }

  const totalItems = order.details.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="max-w-4xl mx-auto px-4 py-8 space-y-6 mt-40">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4" />
          TRỞ LẠI
        </Button>
        <div className="text-center">
          <div className="text-sm text-gray-500">MÃ ĐƠN HÀNG: {order.id.slice(0, 12).toUpperCase()}</div>
          <Badge variant={order.completedAt ? "default" : "secondary"} className="mt-1">
            {order.completedAt ? "ĐƠN HÀNG ĐÃ HOÀN THÀNH" : "ĐANG XỬ LÝ"}
          </Badge>
        </div>
        <div></div> {/* Spacer for center alignment */}
      </div>

      {/* Order Tracking Timeline */}
      <OrderTrackingTimeline order={order} />

      {/* Delivery Address Card */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Địa Chỉ Nhận Hàng
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="font-medium">Nguyễn Thị Hồng Hạnh</div>
            <div className="text-sm text-gray-600">(+84) 987570351</div>
            <div className="text-sm text-gray-600">
              Gần Bách Hoá Mỹ Lệ, Bưng Ông Thoàn, Phường Phú Hữu, Thành Phố Thủ Đức, TP. Hồ Chí Minh
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Tổng tiền hàng</span>
              <span>₫{order.totalAmount.toLocaleString("vi-VN")}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Phí vận chuyển</span>
              <span>₫45.100</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Giảm giá phí vận chuyển</span>
              <span className="text-green-600">-₫45.100</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Voucher từ Shopee</span>
              <span className="text-green-600">-₫36.300</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Voucher từ Shop</span>
              <span className="text-green-600">-₫15.000</span>
            </div>
            <Separator />
            <div className="flex justify-between items-center text-lg font-semibold">
              <span>Thành tiền</span>
              <span className="text-red-500">₫{order.payment.netAmount.toLocaleString("vi-VN")}</span>
            </div>
            <div className="text-sm text-gray-500">
              Phương thức Thanh toán: {order.payment.method}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Sản phẩm đã đặt</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-4">
            {order.details.map((item, index) => (
              <div key={index} className="flex items-start gap-4 p-4 border rounded-lg">
                {/* Product Image Placeholder */}
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center">
                  <Package className="w-8 h-8 text-gray-400" />
                </div>

                {/* Product Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-gray-900 truncate">
                    {item.blindBoxName || item.productName}
                  </h3>
                  {item.blindBoxId && (
                    <Badge variant="secondary" className="mt-1 text-xs">
                      Blindbox
                    </Badge>
                  )}
                  <div className="text-sm text-gray-500 mt-1">
                    Phân loại hàng: {item.blindBoxId ? "ĐIỀU LỆNH" : "DEN VN 2-9 L"}
                  </div>
                  <div className="text-sm text-gray-500">
                    x{item.quantity}
                  </div>
                </div>

                {/* Price */}
                <div className="text-right">
                  <div className="text-sm text-gray-500 line-through">
                    ₫{(item.totalPrice * 1.2).toLocaleString("vi-VN")}
                  </div>
                  <div className="font-semibold text-red-500">
                    ₫{item.totalPrice.toLocaleString("vi-VN")}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-end">
        <Button variant="outline" className="flex-1 sm:flex-none">
          Mua Lại
        </Button>
        <Button variant="outline" className="flex-1 sm:flex-none">
          Liên Hệ Người Bán
        </Button>
      </div>

      {/* Support Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Hỗ trợ</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={tab} onValueChange={setTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="order">Vấn đề đơn hàng</TabsTrigger>
              <TabsTrigger value="shipping">Thông tin giao hàng</TabsTrigger>
              <TabsTrigger value="return">Trả hàng</TabsTrigger>
            </TabsList>

            <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-4 rounded-md border">
              <TabsContent value="order">
                Nếu bạn gặp sự cố với đơn hàng, vui lòng liên hệ chúng tôi qua hotline hoặc gửi yêu cầu hỗ trợ trong mục Liên hệ.
              </TabsContent>
              <TabsContent value="shipping">
                Đơn hàng của bạn sẽ được giao trong vòng 3–5 ngày làm việc. Vui lòng kiểm tra tình trạng giao hàng trong phần Theo dõi đơn hàng.
              </TabsContent>
              <TabsContent value="return">
                Bạn có thể trả hàng trong vòng 7 ngày nếu sản phẩm lỗi hoặc không đúng mô tả. Truy cập mục Chính sách hoàn trả để biết thêm chi tiết.
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
