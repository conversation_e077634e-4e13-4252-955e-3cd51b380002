import { useState, useEffect, useCallback } from "react";
import { getProvinces, getDistricts, getWards } from "@/services/shipping/api-services";
import { ResponseProvinces, ResponseDistricts, ResponseWards } from "@/services/shipping/typings";

export type SimpleAddress = {
  name: string;
  code: string;
};

export type Province = {
  code: string;
  name: string;
  districts: District[];
  provinceID?: number;
};

export type District = {
  code: string;
  name: string;
  wards: Ward[];
  districtID?: number;
};

export type Ward = {
  code: string;
  name: string;
  level?: string;
  wardCode?: number;
};

export default function useAddressData() {
  const [provincesData, setProvincesData] = useState<Province[]>([]);
  const [loadingProvinces, setLoadingProvinces] = useState(false);
  const [loadingDistricts, setLoadingDistricts] = useState<Record<string, boolean>>({});
  const [loadingWards, setLoadingWards] = useState<Record<string, boolean>>({});

  // Fetch provinces on mount
  useEffect(() => {
    const fetchProvinces = async () => {
      try {
        setLoadingProvinces(true);
        const provinces = await getProvinces();

        const parsed: Province[] = provinces.map((p: ResponseProvinces) => ({
          name: p.provinceName,
          code: p.provinceID.toString(),
          provinceID: p.provinceID,
          districts: [], // Will be loaded dynamically
        }));

        setProvincesData(parsed);
      } catch (error) {
        console.error("Failed to fetch provinces:", error);
      } finally {
        setLoadingProvinces(false);
      }
    };

    fetchProvinces();
  }, []);

  // Load districts for a province
  const loadDistricts = useCallback(async (provinceCode: string) => {
    const provinceID = parseInt(provinceCode);
    if (isNaN(provinceID)) return;

    // Check if already loaded
    const province = provincesData.find(p => p.code === provinceCode);
    if (province && province.districts.length > 0) return;

    try {
      setLoadingDistricts(prev => ({ ...prev, [provinceCode]: true }));
      const districts = await getDistricts({ provinceId: provinceID });

      const parsedDistricts: District[] = districts.map((d: ResponseDistricts) => ({
        name: d.districtName,
        code: d.districtID.toString(),
        districtID: d.districtID,
        wards: [], // Will be loaded dynamically
      }));

      setProvincesData(prev =>
        prev.map(p =>
          p.code === provinceCode
            ? { ...p, districts: parsedDistricts }
            : p
        )
      );
    } catch (error) {
      console.error("Failed to fetch districts:", error);
    } finally {
      setLoadingDistricts(prev => ({ ...prev, [provinceCode]: false }));
    }
  }, [provincesData]);

  // Load wards for a district
  const loadWards = useCallback(async (provinceCode: string, districtCode: string) => {
    const districtID = parseInt(districtCode);
    if (isNaN(districtID)) return;

    // Check if already loaded
    const province = provincesData.find(p => p.code === provinceCode);
    const district = province?.districts.find(d => d.code === districtCode);
    if (district && district.wards.length > 0) return;

    try {
      setLoadingWards(prev => ({ ...prev, [districtCode]: true }));
      const wards = await getWards({ districtId: districtID });

      const parsedWards: Ward[] = wards.map((w: ResponseWards) => ({
        name: w.wardName,
        code: w.wardCode.toString(),
        wardCode: w.wardCode,
      }));

      setProvincesData(prev =>
        prev.map(p =>
          p.code === provinceCode
            ? {
                ...p,
                districts: p.districts.map(d =>
                  d.code === districtCode
                    ? { ...d, wards: parsedWards }
                    : d
                )
              }
            : p
        )
      );
    } catch (error) {
      console.error("Failed to fetch wards:", error);
    } finally {
      setLoadingWards(prev => ({ ...prev, [districtCode]: false }));
    }
  }, [provincesData]);

  return {
    provincesData,
    loadingProvinces,
    loadingDistricts,
    loadingWards,
    loadDistricts,
    loadWards,
  };
}
