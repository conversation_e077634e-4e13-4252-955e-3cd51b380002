import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useEffect, useState } from "react";

type SimpleAddress = { name: string; id: number }; // ✅ Use 'id' to match BE
type Ward = SimpleAddress;
type District = SimpleAddress & { wards: Ward[] };
type Province = SimpleAddress & { districts: District[] };

type ProvinceSelectProps = {
  value?: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  };
  onChange: (value: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  }) => void;
  provincesData: Province[];
};

export default function ProvinceSelect({ value, onChange, provincesData }: ProvinceSelectProps) {
  const [internalValue, setInternalValue] = useState<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  useEffect(() => {
    if (value?.province && provincesData.length > 0) {
      const selectedProvince = provincesData.find(
        (p) => p.id === value.province?.id
      );

      setInternalValue((prev) => ({
        ...prev,
        province: selectedProvince || null,
        district: null,
        ward: null,
      }));
    }
    if (!value?.province) {
      setInternalValue({ province: null, district: null, ward: null });
    }
  }, [value?.province, provincesData]);

  useEffect(() => {
    if (internalValue.province && value?.district) {
      const selectedDistrict = internalValue.province.districts.find(
        (d) => d.id === value.district?.id
      );
      setInternalValue((prev) => ({
        ...prev,
        district: selectedDistrict || null,
        ward: null,
      }));
    }
    if (!value?.district) {
      setInternalValue((prev) => ({ ...prev, district: null, ward: null }));
    }
  }, [internalValue.province, value?.district]);

  useEffect(() => {
    if (internalValue.district && value?.ward) {
      const selectedWard = internalValue.district.wards.find(
        (w) => w.id === value.ward?.id
      );
      setInternalValue((prev) => ({
        ...prev,
        ward: selectedWard || null,
      }));
    }
    if (!value?.ward) {
      setInternalValue((prev) => ({ ...prev, ward: null }));
    }
  }, [internalValue.district, value?.ward]);

  const handleProvinceChange = (code: string) => {
    const province = provincesData.find((p) => p.id === parseInt(code)) || null;
    setInternalValue({
      province,
      district: null,
      ward: null,
    });
    onChange({
      province: province ? { name: province.name, id: province.id } : null,
      district: null,
      ward: null,
    });
  };

  const handleDistrictChange = (code: string) => {
    const district = internalValue.province?.districts.find((d) => d.id === parseInt(code)) || null;
    setInternalValue((prev) => ({
      ...prev,
      district,
      ward: null,
    }));
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: district ? { name: district.name, id: district.id } : null,
      ward: null,
    });
  };

  const handleWardChange = (code: string) => {
    const ward = internalValue.district?.wards.find((w) => w.id === parseInt(code)) || null;
    setInternalValue((prev) => ({
      ...prev,
      ward,
    }));
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: internalValue.district
        ? { name: internalValue.district.name, id: internalValue.district.id }
        : null,
      ward: ward ? { name: ward.name, id: ward.id } : null,
    });
  };

  return (
    <div className="space-y-4">

      <Select
        value={internalValue.province?.id?.toString() || ""}
        onValueChange={handleProvinceChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Tỉnh/Thành phố">
            {internalValue.province?.name || value?.province?.name}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {provincesData.map((p) => (
            <SelectItem key={p.id} value={p.id.toString()}>
              {p.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!internalValue.province}
        value={internalValue.district?.id?.toString() || ""}
        onValueChange={handleDistrictChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Quận/Huyện">
            {internalValue.district?.name || value?.district?.name}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {(internalValue.province?.districts ?? []).map((d) => (
            <SelectItem key={d.id} value={d.id.toString()}>
              {d.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!internalValue.district}
        value={internalValue.ward?.code?.toString() || ""}
        onValueChange={handleWardChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Phường/Xã">
            {internalValue.ward?.name || value?.ward?.name}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {(internalValue.district?.wards ?? []).map((w) => (
            <SelectItem key={w.code} value={w.code.toString()}>
              {w.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}