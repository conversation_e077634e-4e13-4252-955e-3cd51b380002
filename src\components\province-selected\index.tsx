import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useEffect, useState } from "react";

type SimpleAddress = { name: string; id: number }; // ✅ Use 'id' to match BE
type Ward = SimpleAddress;
type District = SimpleAddress & { wards: Ward[] };
type Province = SimpleAddress & { districts: District[] };

type ProvinceSelectProps = {
  value?: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  };
  onChange: (value: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  }) => void;
  provincesData: Province[];
};

export default function ProvinceSelect({ value, onChange, provincesData }: ProvinceSelectProps) {
  const [internalValue, setInternalValue] = useState<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  // Sync value prop to internal state (with stable dependencies)
  useEffect(() => {
    if (value?.province?.id && provincesData.length > 0) {
      const selectedProvince = provincesData.find(
        (p) => p.id === value.province?.id
      );

      if (selectedProvince && selectedProvince.id !== internalValue.province?.id) {
        setInternalValue((prev) => ({
          ...prev,
          province: selectedProvince,
          district: null,
          ward: null,
        }));
      }
    } else if (!value?.province?.id) {
      if (internalValue.province !== null) {
        setInternalValue({ province: null, district: null, ward: null });
      }
    }
  }, [value?.province?.id, provincesData.length]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.province && value?.district?.id) {
      const selectedDistrict = internalValue.province.districts.find(
        (d) => d.id === value.district?.id
      );
      if (selectedDistrict && selectedDistrict.id !== internalValue.district?.id) {
        setInternalValue((prev) => ({
          ...prev,
          district: selectedDistrict,
          ward: null,
        }));
      }
    } else if (!value?.district?.id && internalValue.district !== null) {
      setInternalValue((prev) => ({ ...prev, district: null, ward: null }));
    }
  }, [internalValue.province?.id, value?.district?.id]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.district && value?.ward?.id) {
      const selectedWard = internalValue.district.wards.find(
        (w) => w.id === value.ward?.id
      );
      if (selectedWard && selectedWard.id !== internalValue.ward?.id) {
        setInternalValue((prev) => ({
          ...prev,
          ward: selectedWard,
        }));
      }
    } else if (!value?.ward?.id && internalValue.ward !== null) {
      setInternalValue((prev) => ({ ...prev, ward: null }));
    }
  }, [internalValue.district?.id, value?.ward?.id]); // ✅ Use stable primitive values

  const handleProvinceChange = (code: string) => {
    const province = provincesData.find((p) => p.id === parseInt(code)) || null;
    setInternalValue({
      province,
      district: null,
      ward: null,
    });
    onChange({
      province: province ? { name: province.name, id: province.id } : null,
      district: null,
      ward: null,
    });
  };

  const handleDistrictChange = (code: string) => {
    const district = internalValue.province?.districts.find((d) => d.id === parseInt(code)) || null;
    setInternalValue((prev) => ({
      ...prev,
      district,
      ward: null,
    }));
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: district ? { name: district.name, id: district.id } : null,
      ward: null,
    });
  };

  const handleWardChange = (code: string) => {
    const ward = internalValue.district?.wards.find((w) => w.id === parseInt(code)) || null;
    setInternalValue((prev) => ({
      ...prev,
      ward,
    }));
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: internalValue.district
        ? { name: internalValue.district.name, id: internalValue.district.id }
        : null,
      ward: ward ? { name: ward.name, id: ward.id } : null,
    });
  };



  // Temporary debug
  const selectValue = internalValue.province?.id?.toString() || "";
  console.log("🔍 Select Debug:");
  console.log("- selectValue:", selectValue);
  console.log("- internalValue.province:", internalValue.province);
  console.log("- First 3 options:", provincesData.slice(0, 3).map(p => ({ id: p.id, name: p.name, stringId: p.id.toString() })));

  return (
    <div className="space-y-4">
      {/* Debug info */}
      <div className="text-xs bg-yellow-100 p-2 rounded">
        Debug: selectValue = "{selectValue}" | internalValue.province = {internalValue.province?.name || 'null'}
      </div>

      <Select
        value={selectValue}
        onValueChange={handleProvinceChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Tỉnh/Thành phố" />
        </SelectTrigger>
        <SelectContent>
          {provincesData.map((p) => (
            <SelectItem key={p.id} value={p.id.toString()}>
              {p.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!internalValue.province}
        value={internalValue.district?.id?.toString() || ""}
        onValueChange={handleDistrictChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Quận/Huyện" />
        </SelectTrigger>
        <SelectContent>
          {(internalValue.province?.districts ?? []).map((d) => (
            <SelectItem key={d.id} value={d.id.toString()}>
              {d.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!internalValue.district}
        value={internalValue.ward?.id?.toString() || ""}
        onValueChange={handleWardChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Phường/Xã" />
        </SelectTrigger>
        <SelectContent>
          {(internalValue.district?.wards ?? []).map((w) => (
            <SelectItem key={w.id} value={w.id.toString()}>
              {w.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}