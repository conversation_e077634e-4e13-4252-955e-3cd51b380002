import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useEffect, useState, useRef } from "react";
import useGetDistricts from "@/app/(user)/address-list/hooks/useGetDistricts";
import useGetWards from "@/app/(user)/address-list/hooks/useGetWards";

type SimpleAddress = { name: string; id: number }; // ✅ Use 'id' to match BE
type Ward = SimpleAddress;
type District = SimpleAddress & { wards: Ward[] };
type Province = SimpleAddress & { districts: District[] };

type ProvinceSelectProps = {
  value?: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  };
  onChange: (value: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  }) => void;
  provincesData: Province[];
};

export default function ProvinceSelect({ value, onChange, provincesData }: ProvinceSelectProps) {
  // ✅ Initialize API hooks
  const { getDistrictsApi, isPending: isLoadingDistricts, districts } = useGetDistricts();
  const { getWardsApi, isPending: isLoadingWards, wards } = useGetWards();

  const [internalValue, setInternalValue] = useState<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  // ✅ Track immediate selection for instant UI feedback
  const immediateSelectionRef = useRef<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  // ✅ Track loading states for API calls
  const [loadingDistrictsFor, setLoadingDistrictsFor] = useState<number | null>(null);
  const [loadingWardsFor, setLoadingWardsFor] = useState<{ province: number, district: number } | null>(null);
  const [provincesDataWithAPI, setProvincesDataWithAPI] = useState<Province[]>(provincesData);
  const [resetCounter, setResetCounter] = useState(0); // ✅ Force re-render counter

  // ✅ Sync provincesDataWithAPI when provincesData prop changes
  useEffect(() => {
    setProvincesDataWithAPI(provincesData);
  }, [provincesData]);

  // Sync value prop to internal state (with stable dependencies)
  useEffect(() => {
    if (value?.province?.id && provincesData.length > 0) {
      const selectedProvince = provincesData.find(
        (p) => p.id === value.province?.id
      );

      if (selectedProvince && selectedProvince.id !== internalValue.province?.id) {
        setInternalValue((prev) => ({
          ...prev,
          province: selectedProvince,
          district: null,
          ward: null,
        }));
      }
    } else if (!value?.province?.id) {
      if (internalValue.province !== null) {
        setInternalValue({ province: null, district: null, ward: null });
      }
    }
  }, [value?.province?.id, provincesData.length]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.province && value?.district?.id) {
      const selectedDistrict = internalValue.province.districts.find(
        (d) => d.id === value.district?.id
      );
      if (selectedDistrict && selectedDistrict.id !== internalValue.district?.id) {
        setInternalValue((prev) => ({
          ...prev,
          district: selectedDistrict,
          ward: null,
        }));
      }
    } else if (!value?.district?.id && internalValue.district !== null) {
      setInternalValue((prev) => ({ ...prev, district: null, ward: null }));
    }
  }, [internalValue.province?.id, value?.district?.id]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.district && value?.ward?.id) {
      const selectedWard = internalValue.district.wards.find(
        (w) => w.id === value.ward?.id
      );
      if (selectedWard && selectedWard.id !== internalValue.ward?.id) {
        setInternalValue((prev) => ({
          ...prev,
          ward: selectedWard,
        }));
      }
    } else if (!value?.ward?.id && internalValue.ward !== null) {
      setInternalValue((prev) => ({ ...prev, ward: null }));
    }
  }, [internalValue.district?.id, value?.ward?.id]); // ✅ Use stable primitive values

  // ✅ Handle districts API response
  useEffect(() => {
    if (districts.length > 0 && loadingDistrictsFor) {
      const parsedDistricts = districts.map((d) => ({
        name: d.districtName,
        id: d.districtID,
        wards: [] // Will be populated when ward is selected
      }));

      setProvincesDataWithAPI(prev =>
        prev.map(p =>
          p.id === loadingDistrictsFor
            ? { ...p, districts: parsedDistricts }
            : p
        )
      );
      setLoadingDistrictsFor(null); // Clear loading state
    }
  }, [districts, loadingDistrictsFor]);

  // ✅ Handle wards API response
  useEffect(() => {
    if (wards.length > 0 && loadingWardsFor) {
      const parsedWards = wards.map((w) => ({
        name: w.wardName,
        id: w.wardCode
      }));

      setProvincesDataWithAPI(prev =>
        prev.map(p =>
          p.id === loadingWardsFor.province
            ? {
              ...p,
              districts: p.districts.map(d =>
                d.id === loadingWardsFor.district
                  ? { ...d, wards: parsedWards }
                  : d
              )
            }
            : p
        )
      );
      setLoadingWardsFor(null); // Clear loading state
    }
  }, [wards, loadingWardsFor]);

  // 🔍 Debug state changes
  useEffect(() => {
    console.log("📊 internalValue.ward changed:", internalValue.ward);
  }, [internalValue.ward]);

  const handleProvinceChange = (code: string) => {
    const province = provincesData.find((p) => p.id === parseInt(code)) || null;

    // ✅ FORCE complete reset when province changes
    console.log("🔄 RESETTING for new province:", province?.name);

    // Reset ref immediately
    immediateSelectionRef.current = {
      province,
      district: null,
      ward: null,
    };

    // Force reset provincesDataWithAPI to original data
    setProvincesDataWithAPI([...provincesData]); // New array reference

    // Clear any pending API calls
    setLoadingDistrictsFor(null);
    setLoadingWardsFor(null);

    // ✅ Force complete UI re-render
    setResetCounter(prev => prev + 1);

    console.log("✅ Reset complete. Ref:", immediateSelectionRef.current);

    // ✅ Update state for next render
    const newInternalValue = {
      province,
      district: null,
      ward: null,
    };
    setInternalValue(newInternalValue);

    // ✅ Call onChange to notify parent
    onChange({
      province: province ? { name: province.name, id: province.id } : null,
      district: null,
      ward: null,
    });
  };

  const handleDistrictChange = async (code: string) => {
    // ✅ Use currentProvince (includes ref data) instead of internalValue.province
    const sourceProvinceId = immediateSelectionRef.current.province?.id || internalValue.province?.id;
    const sourceProvince = sourceProvinceId ? provincesData.find(p => p.id === sourceProvinceId) : null;
    const district = sourceProvince?.districts.find((d) => d.id === parseInt(code)) || null;

    // ✅ Reset ward data when district changes
    immediateSelectionRef.current = {
      ...immediateSelectionRef.current,
      district,
      ward: null,
    };

    // ✅ Clear old ward data from provincesDataWithAPI
    if (sourceProvince) {
      setProvincesDataWithAPI(prev =>
        prev.map(p =>
          p.id === sourceProvince.id
            ? {
              ...p,
              districts: p.districts.map(d => ({ ...d, wards: [] })) // Clear all wards
            }
            : p
        )
      );
    }

    // ✅ Update state for next render
    const newInternalValue = {
      ...internalValue,
      district,
      ward: null,
    };
    setInternalValue(newInternalValue);

    // ✅ Call wards API to fetch wards for selected district
    if (district && sourceProvince) {
      try {
        setLoadingWardsFor({ province: sourceProvince.id, district: district.id });
        await getWardsApi({ districtId: district.id });
        // Wards will be updated via useEffect when hook state changes
      } catch (error) {
        console.error("Error loading wards:", error);
        setLoadingWardsFor(null); // Clear loading state on error
      }
    }

    // ✅ Call onChange to notify parent
    onChange({
      province: sourceProvince
        ? { name: sourceProvince.name, id: sourceProvince.id }
        : null,
      district: district ? { name: district.name, id: district.id } : null,
      ward: null,
    });
  };

  const handleWardChange = (code: string) => {
    console.log("🔍 handleWardChange called with code:", code);
    // ✅ Find ward from API-fetched data
    const sourceProvinceId = immediateSelectionRef.current.province?.id || internalValue.province?.id;
    const sourceDistrictId = immediateSelectionRef.current.district?.id || internalValue.district?.id;

    const availableWards = provincesDataWithAPI
      .find(p => p.id === sourceProvinceId)?.districts
      .find(d => d.id === sourceDistrictId)?.wards || [];

    console.log("🔍 Available wards:", availableWards.map(w => ({ id: w.id, name: w.name, type: typeof w.id })));
    console.log("🔍 Looking for ward ID (string):", code);

    const wardFromAPI = availableWards.find(w => w.id.toString() === code) || null;
    console.log("🔍 Found ward:", wardFromAPI);

    // ✅ Immediately update ref for instant UI feedback
    console.log("🔄 Updating ref with ward:", wardFromAPI);
    immediateSelectionRef.current = {
      ...immediateSelectionRef.current,
      ward: wardFromAPI,
    };
    console.log("✅ Ref updated:", immediateSelectionRef.current.ward);

    // ✅ Update state for next render
    const newInternalValue = {
      ...internalValue,
      ward: wardFromAPI,
    };
    console.log("🔄 Setting internal value:", newInternalValue.ward);
    setInternalValue(newInternalValue);

    // ✅ Call onChange to notify parent
    const sourceProvince = immediateSelectionRef.current.province || internalValue.province;
    const sourceDistrict = immediateSelectionRef.current.district || internalValue.district;
    onChange({
      province: sourceProvince
        ? { name: sourceProvince.name, id: sourceProvince.id }
        : null,
      district: sourceDistrict
        ? { name: sourceDistrict.name, id: sourceDistrict.id }
        : null,
      ward: wardFromAPI ? { name: wardFromAPI.name, id: wardFromAPI.id } : null,
    });
  };



  // ✅ Always use original provincesData for districts, provincesDataWithAPI for wards only
  const currentProvinceId = immediateSelectionRef.current.province?.id ||
    internalValue.province?.id ||
    value?.province?.id;
  const currentProvince = currentProvinceId ?
    provincesData.find(p => p.id === currentProvinceId) : null;

  // ✅ Get wards from API data if available - but only if district belongs to current province
  const currentDistrictId = immediateSelectionRef.current.district?.id || internalValue.district?.id;
  const currentDistrictWithWards = (currentDistrictId && currentProvinceId) ?
    provincesDataWithAPI.find(p => p.id === currentProvinceId)?.districts.find(d => d.id === currentDistrictId) : null;
  const selectValue = currentProvince?.id?.toString() || "";

  // 🔍 Comprehensive Debug
  console.log("=== RENDER DEBUG ===");
  console.log("currentProvinceId:", currentProvinceId);
  console.log("currentProvince:", currentProvince?.name);
  console.log("currentProvince.districts:", currentProvince?.districts?.map(d => d.name));
  console.log("immediateSelectionRef.district:", immediateSelectionRef.current.district?.name);
  console.log("internalValue.district:", internalValue.district?.name);
  console.log("currentDistrictId:", currentDistrictId);
  console.log("currentDistrictWithWards:", currentDistrictWithWards?.name);
  console.log("currentDistrictWithWards.wards:", currentDistrictWithWards?.wards?.map(w => ({ id: w.id, name: w.name, type: typeof w.id })));
  console.log("🔍 District belongs to current province:", currentProvince?.districts?.some(d => d.id === currentDistrictId));

  // 🔍 Ward Select Debug
  const wardSelectValue = internalValue.ward?.id?.toString() || ""; // ✅ Use state only
  console.log("wardSelectValue (from state):", wardSelectValue);
  console.log("internalValue.ward:", internalValue.ward);
  console.log("internalValue.ward.id type:", typeof internalValue.ward?.id);

  // 🔍 Check if selected ward exists in options
  const wardExists = currentDistrictWithWards?.wards?.find(w => w.id.toString() === wardSelectValue);
  console.log("🔍 Ward exists in options:", wardExists);
  console.log("🔍 Available ward IDs:", currentDistrictWithWards?.wards?.map(w => w.id.toString()));
  console.log("===================");

  return (
    <div className="space-y-4">
      <Select
        key={`province-${selectValue}-${provincesData.length}`}
        value={selectValue}
        onValueChange={handleProvinceChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Tỉnh/Thành phố" />
        </SelectTrigger>
        <SelectContent>
          {provincesData.map((p) => (
            <SelectItem key={p.id} value={p.id.toString()}>
              {p.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        key={`district-${currentProvinceId}-${Date.now()}`} // ✅ Force complete re-render
        disabled={!currentProvince}
        value={(currentProvince && internalValue.district?.id && currentProvince.districts?.some(d => d.id === internalValue.district?.id)) ? internalValue.district.id.toString() : ""} // ✅ Only show if district belongs to current province
        onValueChange={handleDistrictChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Quận/Huyện" />
        </SelectTrigger>
        <SelectContent>
          {(currentProvince?.districts ?? []).map((d) => (
            <SelectItem key={d.id} value={d.id.toString()}>
              {d.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        key={`ward-${currentProvinceId}-${currentDistrictId}-${Date.now()}`} // ✅ Force complete re-render
        disabled={!(immediateSelectionRef.current.district || internalValue.district)}
        value={(currentDistrictWithWards && internalValue.ward?.id && currentDistrictWithWards.wards?.some(w => w.id.toString() === internalValue.ward?.id?.toString())) ? internalValue.ward.id.toString() : ""} // ✅ Only show if ward belongs to current district
        onValueChange={handleWardChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Phường/Xã" />
        </SelectTrigger>
        <SelectContent>
          {(currentDistrictWithWards?.wards ?? []).map((w) => (
            <SelectItem key={w.id} value={w.id.toString()}>
              {w.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}