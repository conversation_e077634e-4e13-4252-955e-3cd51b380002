import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useEffect, useState, useRef } from "react";

type SimpleAddress = { name: string; id: number }; // ✅ Use 'id' to match BE
type Ward = SimpleAddress;
type District = SimpleAddress & { wards: Ward[] };
type Province = SimpleAddress & { districts: District[] };

type ProvinceSelectProps = {
  value?: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  };
  onChange: (value: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  }) => void;
  provincesData: Province[];
};

export default function ProvinceSelect({ value, onChange, provincesData }: ProvinceSelectProps) {
  const [internalValue, setInternalValue] = useState<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  // ✅ Track immediate selection for instant UI feedback
  const immediateSelectionRef = useRef<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  // Sync value prop to internal state (with stable dependencies)
  useEffect(() => {
    if (value?.province?.id && provincesData.length > 0) {
      const selectedProvince = provincesData.find(
        (p) => p.id === value.province?.id
      );

      if (selectedProvince && selectedProvince.id !== internalValue.province?.id) {
        setInternalValue((prev) => ({
          ...prev,
          province: selectedProvince,
          district: null,
          ward: null,
        }));
      }
    } else if (!value?.province?.id) {
      if (internalValue.province !== null) {
        setInternalValue({ province: null, district: null, ward: null });
      }
    }
  }, [value?.province?.id, provincesData.length]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.province && value?.district?.id) {
      const selectedDistrict = internalValue.province.districts.find(
        (d) => d.id === value.district?.id
      );
      if (selectedDistrict && selectedDistrict.id !== internalValue.district?.id) {
        setInternalValue((prev) => ({
          ...prev,
          district: selectedDistrict,
          ward: null,
        }));
      }
    } else if (!value?.district?.id && internalValue.district !== null) {
      setInternalValue((prev) => ({ ...prev, district: null, ward: null }));
    }
  }, [internalValue.province?.id, value?.district?.id]); // ✅ Use stable primitive values

  useEffect(() => {
    if (internalValue.district && value?.ward?.id) {
      const selectedWard = internalValue.district.wards.find(
        (w) => w.id === value.ward?.id
      );
      if (selectedWard && selectedWard.id !== internalValue.ward?.id) {
        setInternalValue((prev) => ({
          ...prev,
          ward: selectedWard,
        }));
      }
    } else if (!value?.ward?.id && internalValue.ward !== null) {
      setInternalValue((prev) => ({ ...prev, ward: null }));
    }
  }, [internalValue.district?.id, value?.ward?.id]); // ✅ Use stable primitive values

  const handleProvinceChange = (code: string) => {
    const province = provincesData.find((p) => p.id === parseInt(code)) || null;

    // ✅ Immediately update ref for instant UI feedback
    immediateSelectionRef.current = {
      province,
      district: null,
      ward: null,
    };

    // ✅ Update state for next render
    const newInternalValue = {
      province,
      district: null,
      ward: null,
    };
    setInternalValue(newInternalValue);

    // ✅ Call onChange to notify parent
    onChange({
      province: province ? { name: province.name, id: province.id } : null,
      district: null,
      ward: null,
    });
  };

  const handleDistrictChange = (code: string) => {
    const district = internalValue.province?.districts.find((d) => d.id === parseInt(code)) || null;

    // ✅ Immediately update internal state
    const newInternalValue = {
      ...internalValue,
      district,
      ward: null,
    };
    setInternalValue(newInternalValue);

    // ✅ Call onChange to notify parent
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: district ? { name: district.name, id: district.id } : null,
      ward: null,
    });
  };

  const handleWardChange = (code: string) => {
    const ward = internalValue.district?.wards.find((w) => w.id === parseInt(code)) || null;

    // ✅ Immediately update internal state
    const newInternalValue = {
      ...internalValue,
      ward,
    };
    setInternalValue(newInternalValue);

    // ✅ Call onChange to notify parent
    onChange({
      province: internalValue.province
        ? { name: internalValue.province.name, id: internalValue.province.id }
        : null,
      district: internalValue.district
        ? { name: internalValue.district.name, id: internalValue.district.id }
        : null,
      ward: ward ? { name: ward.name, id: ward.id } : null,
    });
  };



  // ✅ Use immediate ref OR internalValue OR value prop for instant display
  const currentProvince = immediateSelectionRef.current.province ||
    internalValue.province ||
    (value?.province?.id ? provincesData.find(p => p.id === value.province?.id) : null);
  const selectValue = currentProvince?.id?.toString() || "";

  // Debug timing issue
  console.log("🔍 Render Debug:");
  console.log("- immediateSelectionRef.province:", immediateSelectionRef.current.province?.name || 'null');
  console.log("- internalValue.province:", internalValue.province?.name || 'null');
  console.log("- value?.province:", value?.province?.name || 'null');
  console.log("- currentProvince:", currentProvince?.name || 'null');
  console.log("- selectValue:", selectValue);

  return (
    <div className="space-y-4">

      <Select
        key={`province-${selectValue}-${provincesData.length}`}
        value={selectValue}
        onValueChange={handleProvinceChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Tỉnh/Thành phố" />
        </SelectTrigger>
        <SelectContent>
          {provincesData.map((p) => (
            <SelectItem key={p.id} value={p.id.toString()}>
              {p.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!currentProvince}
        value={internalValue.district?.id?.toString() || (value?.district?.id ?
          currentProvince?.districts.find(d => d.id === value.district?.id)?.id?.toString() || "" : "")}
        onValueChange={handleDistrictChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Quận/Huyện" />
        </SelectTrigger>
        <SelectContent>
          {(currentProvince?.districts ?? []).map((d) => (
            <SelectItem key={d.id} value={d.id.toString()}>
              {d.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select
        disabled={!internalValue.district}
        value={internalValue.ward?.id?.toString() || (value?.ward?.id ?
          internalValue.district?.wards.find(w => w.id === value.ward?.id)?.id?.toString() || "" : "")}
        onValueChange={handleWardChange}
      >
        <SelectTrigger>
          <SelectValue placeholder="Phường/Xã" />
        </SelectTrigger>
        <SelectContent>
          {(internalValue.district?.wards ?? []).map((w) => (
            <SelectItem key={w.id} value={w.id.toString()}>
              {w.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}