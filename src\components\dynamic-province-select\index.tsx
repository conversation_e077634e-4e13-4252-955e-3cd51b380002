import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { useEffect, useState } from "react";
import { SimpleAddress, Province, District, Ward } from "@/hooks/use-address-data";

type DynamicProvinceSelectProps = {
  value?: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  };
  onChange: (value: {
    province: SimpleAddress | null;
    district: SimpleAddress | null;
    ward: SimpleAddress | null;
  }) => void;
  provincesData: Province[];
  loadingProvinces: boolean;
  loadingDistricts: Record<string, boolean>;
  loadingWards: Record<string, boolean>;
  loadDistricts: (provinceCode: string) => Promise<void>;
  loadWards: (provinceCode: string, districtCode: string) => Promise<void>;
};

export default function DynamicProvinceSelect({
  value,
  onChange,
  provincesData,
  loadingProvinces,
  loadingDistricts,
  loadingWards,
  loadDistricts,
  loadWards,
}: DynamicProvinceSelectProps) {
  const [internalValue, setInternalValue] = useState<{
    province: Province | null;
    district: District | null;
    ward: Ward | null;
  }>({
    province: null,
    district: null,
    ward: null,
  });

  useEffect(() => {
    if (value?.province && provincesData.length > 0) {
      const selectedProvince = provincesData.find(
        (p) => p.code === value.province?.code
      );
      setInternalValue((prev) => ({
        ...prev,
        province: selectedProvince || null,
        district: null,
        ward: null,
      }));
    }
  }, [value?.province, provincesData]);

  useEffect(() => {
    if (value?.district && internalValue.province) {
      const selectedDistrict = internalValue.province.districts.find(
        (d) => d.code === value.district?.code
      );
      setInternalValue((prev) => ({
        ...prev,
        district: selectedDistrict || null,
        ward: null,
      }));
    }
  }, [value?.district, internalValue.province]);

  useEffect(() => {
    if (value?.ward && internalValue.district) {
      const selectedWard = internalValue.district.wards.find(
        (w) => w.code === value.ward?.code
      );
      setInternalValue((prev) => ({
        ...prev,
        ward: selectedWard || null,
      }));
    }
  }, [value?.ward, internalValue.district]);

  const handleProvinceChange = async (provinceCode: string) => {
    const selectedProvince = provincesData.find((p) => p.code === provinceCode);
    if (selectedProvince) {
      setInternalValue({
        province: selectedProvince,
        district: null,
        ward: null,
      });
      onChange({
        province: { name: selectedProvince.name, code: selectedProvince.code },
        district: null,
        ward: null,
      });

      // Load districts if not already loaded
      if (selectedProvince.districts.length === 0) {
        await loadDistricts(provinceCode);
      }
    }
  };

  const handleDistrictChange = async (districtCode: string) => {
    if (!internalValue.province) return;

    const selectedDistrict = internalValue.province.districts.find(
      (d) => d.code === districtCode
    );
    if (selectedDistrict) {
      setInternalValue((prev) => ({
        ...prev,
        district: selectedDistrict,
        ward: null,
      }));
      onChange({
        province: value?.province || null,
        district: { name: selectedDistrict.name, code: selectedDistrict.code },
        ward: null,
      });

      // Load wards if not already loaded
      if (selectedDistrict.wards.length === 0) {
        await loadWards(internalValue.province.code, districtCode);
      }
    }
  };

  const handleWardChange = (wardCode: string) => {
    if (!internalValue.district) return;

    const selectedWard = internalValue.district.wards.find(
      (w) => w.code === wardCode
    );
    if (selectedWard) {
      setInternalValue((prev) => ({
        ...prev,
        ward: selectedWard,
      }));
      onChange({
        province: value?.province || null,
        district: value?.district || null,
        ward: { name: selectedWard.name, code: selectedWard.code },
      });
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Province Select */}
      <div>
        <label className="block text-sm font-medium mb-2">Tỉnh/Thành phố</label>
        <Select
          value={internalValue.province?.code || ""}
          onValueChange={handleProvinceChange}
          disabled={loadingProvinces}
        >
          <SelectTrigger>
            <SelectValue placeholder={loadingProvinces ? "Đang tải..." : "Chọn tỉnh/thành phố"} />
          </SelectTrigger>
          <SelectContent>
            {provincesData.map((province) => (
              <SelectItem key={province.code} value={province.code}>
                {province.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* District Select */}
      <div>
        <label className="block text-sm font-medium mb-2">Quận/Huyện</label>
        <Select
          value={internalValue.district?.code || ""}
          onValueChange={handleDistrictChange}
          disabled={!internalValue.province || loadingDistricts[internalValue.province?.code || ""]}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !internalValue.province 
                  ? "Chọn tỉnh/thành phố trước"
                  : loadingDistricts[internalValue.province.code]
                  ? "Đang tải..."
                  : "Chọn quận/huyện"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            {internalValue.province?.districts.map((district) => (
              <SelectItem key={district.code} value={district.code}>
                {district.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Ward Select */}
      <div>
        <label className="block text-sm font-medium mb-2">Phường/Xã</label>
        <Select
          value={internalValue.ward?.code || ""}
          onValueChange={handleWardChange}
          disabled={!internalValue.district || loadingWards[internalValue.district?.code || ""]}
        >
          <SelectTrigger>
            <SelectValue 
              placeholder={
                !internalValue.district 
                  ? "Chọn quận/huyện trước"
                  : loadingWards[internalValue.district.code]
                  ? "Đang tải..."
                  : "Chọn phường/xã"
              } 
            />
          </SelectTrigger>
          <SelectContent>
            {internalValue.district?.wards.map((ward) => (
              <SelectItem key={ward.code} value={ward.code}>
                {ward.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
