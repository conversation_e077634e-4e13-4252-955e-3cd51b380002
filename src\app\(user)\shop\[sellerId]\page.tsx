import { Metadata } from "next/dist/lib/metadata/types/metadata-interface";
import ShopProducts from "../components/shop-products";

export const metadata: Metadata = {
    title: "C<PERSON><PERSON> hàng",
    description: "<PERSON><PERSON> tất cả sản phẩm của cửa hàng",
};

export default function ShopPage({ params }: { params: { sellerId: string } }) {
    return (
        <div className="w-full">
            <ShopProducts sellerId={params.sellerId} />
        </div>
    );
}
