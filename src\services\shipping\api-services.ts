import request from "@/services/interceptor";
import API_ENDPOINTS from "@/services/shipping/api-path";
import { RequestDistricts, RequestWards, ResponseDistricts, ResponseProvinces, ResponseWards } from "./typings";

export const getProvinces = async (): Promise<ResponseProvinces[]> => {
  const response = await request<ResponseProvinces[]>(
    API_ENDPOINTS.SHIPPING_PROVINCES,
    {
      method: "GET",
    }
  );
  return response.data;
};

export const getDistricts = async (
  params: RequestDistricts
): Promise<ResponseDistricts[]> => {
  const response = await request<ResponseDistricts[]>(
    API_ENDPOINTS.SHIPPING_DISTRICTS,
    {
      method: "GET",
      params,
    }
  );
  return response.data;
};

export const getWards = async (params: RequestWards): Promise<ResponseWards[]> => {
  const response = await request<ResponseWards[]>(API_ENDPOINTS.SHIPPING_WARDS, {
    method: "GET",
    params,
  });
  return response.data;
};
