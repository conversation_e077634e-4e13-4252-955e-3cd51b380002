'use client'

import React, { useEffect, useState } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { InventoryTabs } from '@/components/inventory-tabs'
import {
    <PERSON>,
    CardHeader,
    CardT<PERSON>le,
    Card<PERSON>ontent,
    CardFooter,
} from '@/components/ui/card'
import { <PERSON><PERSON>, Di<PERSON>Content, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Backdrop } from '@/components/backdrop'
import Pagination from '@/components/pagination'
import useGetAllBlindboxInventory from '../hooks/useGetBlindboxInventory'
import useGetAllItemInventory from '../hooks/useGetItemInventory'
import QuickViewDialog from '@/components/alldialog/dialogquickly'
import { Product } from '@/services/inventory-item/typings'
import { BlindBox, CustomerInventory } from '@/services/customer-blindboxes/typings'
import { InventoryItem as ItemInventoryType } from '@/services/inventory-item/typings'
import useGetA<PERSON>Address from '../../address-list/hooks/useGetAllAddress'
import useUnbox from '../hooks/useUnbox'
import useGetItemByBlindBox from '../hooks/useGetItemByBlindBox'
import { InventoryItem as WonInventoryItem } from '@/services/inventory-item/typings'

interface InventoryItem {
    id: string
    title: string
    image: string
    status: 'unopened' | 'opened' | null
    type: 'blindbox' | 'product'
    orderId?: string
    blindBoxId: string
    productId?: string
    product?: Product
    blindbox?: BlindBox
    quantity?: number
    createdAt?: string
    isFromBlindBox?: boolean
}

export default function Inventory() {
    const router = useRouter()
    const [activeTab, setActiveTab] = useState('all')
    const [currentPage, setCurrentPage] = useState(1)
    const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([])
    const [isLoading, setIsLoading] = useState(false)
    const [totalPages, setTotalPages] = useState(1)
    const [totalCount, setTotalCount] = useState(0)
    const [blindboxFilter, setBlindboxFilter] = useState<'all' | 'opened' | 'unopened'>('all')
    const { getAllAddressApi } = useGetAllAddress()
    const [showAddressDialog, setShowAddressDialog] = useState(false)
    const [pendingDeliveryId, setPendingDeliveryId] = useState<string | null>(null)
    const { handleUnbox, isUnboxing } = useUnbox()
    const [showPrizeDialog, setShowPrizeDialog] = useState(false)
    const [selectedPrize, setSelectedPrize] = useState<{
        customerBlindBoxId: string
        blindBoxId: string
        blindBoxName: string
    } | null>(null)

    const { inventoryItem: wonItem, isLoading: loadingPrize, error: prizeError } = useGetItemByBlindBox(
        selectedPrize?.blindBoxId || ''
    )

    const PAGE_SIZE = 8

    const {
        getAllBlindboxInventoryApi,
        isPending: isBlindboxLoading,
    } = useGetAllBlindboxInventory()

    const {
        getAllItemInventoryApi,
        isPending: isItemLoading,
    } = useGetAllItemInventory()

    const handlePageChange = (newPage: number) => {
        if (newPage < 1 || newPage > totalPages) return
        setCurrentPage(newPage)
    }

    const getCurrentPageItems = () => {
        return inventoryItems
    }

    useEffect(() => {
        const fetchInventory = async () => {
            setIsLoading(true)
            try {
                if (activeTab === 'blindbox') {
                    const paginationParams = {
                        pageIndex: currentPage,
                        pageSize: PAGE_SIZE,
                        isOpened: blindboxFilter === 'all' ? undefined : blindboxFilter === 'opened'
                    };

                    const blindboxRes = await getAllBlindboxInventoryApi(paginationParams)
                    if (blindboxRes?.value.data?.result) {
                        const blindboxItems: InventoryItem[] = blindboxRes.value.data.result.map((item: CustomerInventory) => ({
                            id: item.id,
                            blindBoxId: item.blindBoxId,
                            blindbox: item.blindBox,
                            title: item.blindBox?.name || '',
                            image: item.blindBox?.imageUrl ?? '',
                            status: item.isOpened ? 'opened' : 'unopened',
                            type: 'blindbox',
                            orderId: item.orderDetailId,
                            createdAt: item.createdAt,
                        }))
                        const sortedBlindboxItems = blindboxItems.sort((a, b) => {
                            if (!a.createdAt || !b.createdAt) return 0;
                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                        });
                        setInventoryItems(sortedBlindboxItems)
                        setTotalPages(blindboxRes.value.data.totalPages)
                        setTotalCount(blindboxRes.value.data.count)
                    }
                } else if (activeTab === 'all') {
                    const itemRes = await getAllItemInventoryApi({
                        pageIndex: currentPage,
                        pageSize: PAGE_SIZE
                    })

                    if (itemRes?.value.data?.result) {
                        const itemItems: InventoryItem[] = itemRes.value.data.result.map((item: ItemInventoryType) => ({
                            id: item.id,
                            productId: item.productId,
                            product: item.product,
                            title: item.product?.name || '',
                            image: item.product?.imageUrls?.[0] ?? '',
                            status: null,
                            type: 'product',
                            quantity: item.quantity,
                            createdAt: item.createdAt,
                            isFromBlindBox: item.isFromBlindBox,
                            blindBoxId: item.isFromBlindBox ? (item.sourceCustomerBlindBoxId || '') : '',
                        }))

                        const sortedItems = itemItems.sort((a, b) => {
                            if (!a.createdAt || !b.createdAt) return 0;
                            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
                        });

                        setInventoryItems(sortedItems)
                        setTotalPages(itemRes.value.data.totalPages)
                        setTotalCount(itemRes.value.data.count)
                    }
                }
            } catch (error) {
                console.error('Error fetching inventory:', error)
                setInventoryItems([])
            } finally {
                setIsLoading(false)
            }
        }

        fetchInventory()
    }, [activeTab, currentPage, blindboxFilter])

    const handleViewDetail = (id: string, type: 'blindbox' | 'product', blindBoxId?: string, productId?: string) => {
        setIsLoading(true)
        if (type === 'blindbox' && blindBoxId) {
            router.push(`/detail-blindbox/${blindBoxId}`)
        } else if (type === 'product' && productId) {
            router.push(`/detail/${productId}`)
        } else {
            setIsLoading(false)
        }
    }

    const handleOpenBox = (customerBlindBoxId: string) => {
        const blindBoxItem = inventoryItems.find(item => item.id === customerBlindBoxId);
        const blindBoxName = blindBoxItem?.title || 'BlindBox';
        const blindBoxId = blindBoxItem?.blindBoxId;
        handleUnbox(customerBlindBoxId, blindBoxName, blindBoxId);
    }

    const handleViewPrize = (customerBlindBoxId: string) => {
        const blindBoxItem = inventoryItems.find(item => item.id === customerBlindBoxId);
        if (blindBoxItem && blindBoxItem.blindBoxId) {
            setSelectedPrize({
                customerBlindBoxId,
                blindBoxId: blindBoxItem.blindBoxId,
                blindBoxName: blindBoxItem.title
            });
            setShowPrizeDialog(true);
        }
    }

    const handleResellItem = (itemId: string) => {
        // TODO: Implement resell functionality for blindbox prize items
        console.log('Resell item:', itemId);
        // Navigate to resell page or open resell dialog
    }

    const handleDeliver = async (itemId: string) => {
        const res = await getAllAddressApi()
        const defaultAddr = res?.value.data.find(addr => addr.isDefault)

        if (!defaultAddr) {
            setPendingDeliveryId(itemId)
            setShowAddressDialog(true)
            return
        }

        console.log(`Giao hàng: ${itemId}`)
    }

    return (
        <div className="p-4 container mx-auto mt-32">
            <InventoryTabs
                activeTab={activeTab}
                onTabChange={(tab) => {
                    setActiveTab(tab)
                    setCurrentPage(1)
                }}
            />

            {activeTab === 'blindbox' && (
                <div className="flex gap-2 mt-4 mb-2 px-9">
                    <Button
                        variant={blindboxFilter === 'all' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                            setBlindboxFilter('all')
                            setCurrentPage(1)
                        }}
                    >
                        Tất cả
                    </Button>
                    <Button
                        variant={blindboxFilter === 'opened' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                            setBlindboxFilter('opened')
                            setCurrentPage(1)
                        }}
                    >
                        Đã mở
                    </Button>
                    <Button
                        variant={blindboxFilter === 'unopened' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => {
                            setBlindboxFilter('unopened')
                            setCurrentPage(1)
                        }}
                    >
                        Chưa mở
                    </Button>
                </div>
            )}

            {getCurrentPageItems().length === 0 && !isLoading && !isItemLoading && !isBlindboxLoading ? (
                <div className="flex flex-col items-center justify-center py-16 text-center">
                    <img
                        src="images/Empty-item.jpg"
                        alt="Không có sản phẩm"
                        className="w-48 h-48 object-contain mb-4"
                    />
                    <h3 className="text-xl font-semibold text-gray-700 mb-2">
                        {activeTab === 'blindbox'
                            ? 'Chưa có BlindBox nào'
                            : activeTab === 'all'
                                ? 'Chưa có sản phẩm nào trong kho'
                                : 'Chưa có sản phẩm nào'
                        }
                    </h3>
                    <p className="text-gray-500 max-w-md">
                        {activeTab === 'blindbox'
                            ? blindboxFilter === 'opened'
                                ? 'Bạn chưa mở BlindBox nào. Hãy mua và mở BlindBox để xem những gì bên trong!'
                                : blindboxFilter === 'unopened'
                                    ? 'Bạn không có BlindBox chưa mở nào. Hãy mua BlindBox mới!'
                                    : 'Bạn chưa có BlindBox nào. Hãy khám phá và mua BlindBox yêu thích!'
                            : 'Hãy bắt đầu mua sắm để thêm sản phẩm vào kho của bạn!'
                        }
                    </p>
                </div>
            ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-14 mt-6 md:px-9">
                    {getCurrentPageItems().map((item: InventoryItem) => (
                        <Card key={item.id} className="transition-all duration-300 transform hover:scale-105">
                            <CardHeader className='p-0'>
                                <div className="relative group w-full aspect-[3/2] overflow-hidden rounded-t-lg">
                                    <img
                                        src={item.image}
                                        alt={item.title}
                                        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                                    />
                                    {item.quantity && item.quantity > 1 && (
                                        <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                                            {item.quantity}
                                        </div>
                                    )}
                                    <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                        <div className="flex gap-2">
                                            {item.type === 'product' && item.product && (
                                                <QuickViewDialog type="product" data={item.product} />
                                            )}

                                            {item.type === 'blindbox' && item.blindbox && (
                                                <QuickViewDialog type="blindbox" data={item.blindbox} />
                                            )}
                                            <Button
                                                className="text-xs px-3 py-2 rounded-md bg-white text-black hover:bg-gray-300"
                                                onClick={() =>
                                                    handleViewDetail(item.id, item.type, item.blindBoxId, item.productId)
                                                }
                                            >
                                                Xem chi tiết
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </CardHeader>

                            <CardContent className="pt-4">
                                <div className="flex items-center gap-2">
                                    <CardTitle className="truncate text-lg">{item.title}</CardTitle>

                                    {item.type === 'blindbox' && item.status && (
                                        <span
                                            className={`text-sm font-medium ${item.status === 'opened' ? 'text-green-500' : 'text-yellow-500'
                                                }`}
                                        >
                                            ({item.status === 'opened' ? 'Đã mở' : 'Chưa mở'})
                                        </span>
                                    )}
                                </div>
                            </CardContent>

                            <CardFooter className="flex flex-col gap-2">
                                {item.type === 'blindbox' && item.status === 'opened' ? (
                                    <div className="flex gap-2 w-full">
                                        <Button
                                            onClick={() => handleViewPrize(item.id)}
                                            className="flex-1 border border-blue-600 text-blue-600 bg-transparent hover:bg-blue-600 hover:text-white transition"
                                        >
                                            Xem thưởng
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="flex gap-2 w-full">
                                        {item.type === 'product' && !item.isFromBlindBox && (
                                            <Button
                                                onClick={() => handleDeliver(item.id)}
                                                className="flex-1 border border-green-600 text-green-600 bg-transparent hover:bg-green-600 hover:text-white transition"
                                            >
                                                Giao hàng
                                            </Button>
                                        )}
                                        {item.type === 'product' && item.isFromBlindBox && (
                                            <>
                                                <Button
                                                    onClick={() => handleResellItem(item.id)}
                                                    className="flex-1 border border-orange-600 text-orange-600 bg-transparent hover:bg-orange-600 hover:text-white transition"
                                                >
                                                    Bán lại
                                                </Button>
                                                <Button
                                                    onClick={() => handleDeliver(item.id)}
                                                    className="flex-1 border border-green-600 text-green-600 bg-transparent hover:bg-green-600 hover:text-white transition"
                                                >
                                                    Giao hàng
                                                </Button>
                                            </>
                                        )}
                                        {item.type === 'blindbox' && item.status === 'unopened' && (
                                            <Button
                                                onClick={() => handleOpenBox(item.id)}
                                                className="flex-1 border border-red-600 text-red-600 bg-transparent hover:bg-red-600 hover:text-white transition"
                                            >
                                                Mở hộp
                                            </Button>
                                        )}
                                    </div>
                                )}
                            </CardFooter>
                        </Card>
                    ))}
                </div>
            )}

            {getCurrentPageItems().length > 0 && totalPages > 1 && (
                <div className="mt-8 flex justify-center">
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={handlePageChange}
                    />
                </div>
            )}

            <Dialog open={showAddressDialog} onOpenChange={setShowAddressDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Thiếu địa chỉ giao hàng</DialogTitle>
                    </DialogHeader>
                    <div className="text-sm text-muted-foreground">
                        Bạn chưa thiết lập địa chỉ giao hàng mặc định. Vui lòng thêm địa chỉ để tiếp tục.
                    </div>
                    <DialogFooter className="mt-4">
                        <Button variant="outline" onClick={() => setShowAddressDialog(false)}>
                            Đóng
                        </Button>
                        <Button asChild>
                            <Link href="/address-list">Thiết lập địa chỉ</Link>
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            <Dialog open={showPrizeDialog} onOpenChange={setShowPrizeDialog}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Phần thưởng từ {selectedPrize?.blindBoxName}</DialogTitle>
                    </DialogHeader>

                    {loadingPrize ? (
                        <div className="flex justify-center items-center py-8">
                            <div className="text-sm text-muted-foreground">Đang tải thông tin phần thưởng...</div>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="text-sm text-muted-foreground">
                                Phần thưởng bạn đã nhận được từ blindbox này:
                            </div>

                            {wonItem ? (
                                <Card className="border">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col md:flex-row gap-6">
                                            <div className="w-full md:w-48 aspect-square relative bg-gray-100 rounded-lg overflow-hidden">
                                                <img
                                                    src={
                                                        wonItem.product?.imageUrls?.[0] || '/images/item1.png'
                                                    }
                                                    alt={wonItem.product?.name || 'Product'}
                                                    className="w-full h-full object-cover"
                                                />
                                            </div>
                                            <div className="flex-1 space-y-4">
                                                <div>
                                                    <h3 className="text-xl font-semibold mb-2">
                                                        {wonItem.product?.name || 'Sản phẩm'}
                                                    </h3>
                                                    <p className="text-muted-foreground text-sm">
                                                        {wonItem.product?.description || 'Không có mô tả'}
                                                    </p>
                                                </div>

                                                <div className="flex flex-col gap-4 text-sm">
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-semibold">Giá trị:</span>
                                                        <span className="text-green-600">
                                                            {wonItem.product?.price ?
                                                                `${wonItem.product.price.toLocaleString('vi-VN')}đ` :
                                                                'Chưa có giá'
                                                            }
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-semibold">Chiều cao:</span>
                                                        <span>
                                                            {wonItem.product?.height ?
                                                                `${wonItem.product.height} cm` :
                                                                'Chưa có thông tin'}
                                                        </span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <span className="font-semibold">Chất liệu:</span>
                                                        <span>
                                                            {wonItem.product?.material || 'Chưa có thông tin'}
                                                        </span>
                                                    </div>
                                                </div>

                                                <div className="flex gap-3 pt-4">
                                                    <Button
                                                        className="w-1/2 border border-green-600 text-green-600 bg-transparent hover:bg-green-600 hover:text-white transition"
                                                        onClick={() => {
                                                            // TODO: Implement sell functionality
                                                            console.log('Sell item:', wonItem.productId || wonItem.id);
                                                        }}
                                                    >
                                                        Bán lại sản phẩm
                                                    </Button>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    Không tìm thấy thông tin phần thưởng
                                </div>
                            )}
                        </div>
                    )}
                </DialogContent>
            </Dialog>

            <Backdrop open={isLoading || isItemLoading || isBlindboxLoading || isUnboxing} />
        </div >
    )
}
