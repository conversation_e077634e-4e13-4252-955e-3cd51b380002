"use client"

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import ProvinceSelect from "@/components/province-selected"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import useCreateAddress from "@/app/(user)/address-list/hooks/useCreateAddress"
import { useEffect, useState } from "react"
import { Controller } from "react-hook-form"
import useUpdateAddress from "@/app/(user)/address-list/hooks/useUpdateAddress"

import useGetProvinces from "@/app/(user)/address-list/hooks/useGetProvinces"
import useGetDistricts from "@/app/(user)/address-list/hooks/useGetDistricts"
import useGetWards from "@/app/(user)/address-list/hooks/useGetWards"

// Use BE response types directly
type SimpleAddress = {
    name: string
    id: number  // Use 'id' to match BE field names (provinceID, districtID, wardCode)
}

type Province = { id: number; name: string; districts: District[] };
type District = { id: number; name: string; wards: Ward[] };
type Ward = { id: number; name: string };

export default function AddressDialog({
    open,
    onClose,
    editingAddress,
}: {
    open: boolean
    onClose: () => void
    editingAddress: API.ResponseAddress | null
}) {
    const isEditing = !!editingAddress
    const {
        register,
        handleSubmit,
        control,
        onSubmit,
        watch,
        errors,
        setValue,
        isPending,
        reset,
    } = isEditing
            ? useUpdateAddress({
                addressId: editingAddress.id,
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
            })
            : useCreateAddress()

    const [address, setAddress] = useState<{
        province: SimpleAddress | null,
        district: SimpleAddress | null,
        ward: SimpleAddress | null,
    }>({
        province: null,
        district: null,
        ward: null,
    });

    const [provincesData, setProvincesData] = useState<Province[]>([]);

    // Initialize hooks
    const { getProvincesApi, isPending: isLoadingProvinces, provinces } = useGetProvinces();
    const { getDistrictsApi, isPending: isLoadingDistricts, districts } = useGetDistricts();
    const { getWardsApi, isPending: isLoadingWards, wards } = useGetWards();

    // Load provinces on mount
    useEffect(() => {
        const fetchProvinces = async () => {
            await getProvincesApi();
        };

        fetchProvinces();
    }, []); // ✅ Empty dependency - only run once

    // Update provincesData when provinces from hook changes
    useEffect(() => {
        if (provinces.length > 0) {
            const parsed = provinces.map((p) => ({
                name: p.provinceName,
                id: p.provinceID, // ✅ Use correct field names
                districts: [] // Will be loaded dynamically
            }));
            setProvincesData(parsed);
        }
    }, [provinces]); // ✅ Listen to provinces changes from hook

    // Track which province we're loading districts for
    const [loadingDistrictsFor, setLoadingDistrictsFor] = useState<number | null>(null);
    const [loadingWardsFor, setLoadingWardsFor] = useState<{ province: number, district: number } | null>(null);

    // Update districts when districts data changes
    useEffect(() => {
        if (districts.length > 0 && loadingDistrictsFor) {
            const parsedDistricts = districts.map((d) => ({
                name: d.districtName,
                id: d.districtID, // ✅ Use correct field names
                wards: []
            }));

            setProvincesData(prev =>
                prev.map(p =>
                    p.id === loadingDistrictsFor
                        ? { ...p, districts: parsedDistricts }
                        : { ...p, districts: [] } // ✅ Clear districts from other provinces
                )
            );
            setLoadingDistrictsFor(null); // Clear loading state
        }
    }, [districts, loadingDistrictsFor]);

    // Update wards when wards data changes
    useEffect(() => {
        if (wards.length > 0 && loadingWardsFor) {
            const parsedWards = wards.map((w) => ({
                name: w.wardName,
                id: w.wardCode // ✅ Use correct field names (wardCode is the ID)
            }));

            setProvincesData(prev =>
                prev.map(p =>
                    p.id === loadingWardsFor.province
                        ? {
                            ...p,
                            districts: p.districts.map(d =>
                                d.id === loadingWardsFor.district
                                    ? { ...d, wards: parsedWards }
                                    : d
                            )
                        }
                        : p
                )
            );
            setLoadingWardsFor(null); // Clear loading state
        }
    }, [wards, loadingWardsFor]);

    useEffect(() => {
        if (!open || provincesData.length === 0) return;

        if (editingAddress) {
            // For editing, just populate form fields directly
            // Address selection will be handled separately if needed
            reset({
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
                isDefault: editingAddress.isDefault || false,
            });
        } else {
            // For new address, clear everything
            setAddress({
                province: null,
                district: null,
                ward: null,
            });
            reset({
                fullName: "",
                phone: "",
                addressLine: "",
                province: "",
                city: "",
                postalCode: "",
                isDefault: false,
            });
        }
    }, [open, editingAddress, provincesData, reset]);

    useEffect(() => {
    }, []);

    // Load districts when province is selected
    const loadDistrictsForProvince = async (provinceCode: number) => {
        try {
            setLoadingDistrictsFor(provinceCode); // Set loading state
            await getDistrictsApi({ provinceId: provinceCode });
            // Districts will be updated via useEffect when hook state changes
        } catch (error) {
            console.error("Error loading districts:", error);
            setLoadingDistrictsFor(null); // Clear loading state on error
        }
    };

    // Load wards when district is selected
    const loadWardsForDistrict = async (provinceCode: number, districtCode: number) => {
        try {
            setLoadingWardsFor({ province: provinceCode, district: districtCode }); // Set loading state
            await getWardsApi({ districtId: districtCode });
            // Wards will be updated via useEffect when hook state changes
        } catch (error) {
            console.error("Error loading wards:", error);
            setLoadingWardsFor(null); // Clear loading state on error
        }
    };

    const handleSelectChange = async (value: {
        province: SimpleAddress | null
        district: SimpleAddress | null
        ward: SimpleAddress | null
    }) => {
        setAddress(value);

        setValue("province", value.province?.name || "");
        setValue("city", value.district?.name || "");
        setValue("postalCode", value.ward?.id?.toString() || ""); // ✅ Convert number to string for form

        // Load districts when province changes
        if (value.province && value.province !== address.province) {
            const province = provincesData.find(p => p.id === value.province?.id);
            if (province && province.districts.length === 0) {
                await loadDistrictsForProvince(value.province.id);
            }
        }

        // Load wards when district changes
        if (value.district && value.district !== address.district && value.province) {
            const province = provincesData.find(p => p.id === value.province?.id);
            const district = province?.districts.find(d => d.id === value.district?.id);
            if (district && district.wards.length === 0) {
                await loadWardsForDistrict(value.province.id, value.district.id);
            }
        }
    };

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-xl max-h-[100vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">
                        {isEditing ? "Cập nhật địa chỉ" : "Thêm địa chỉ mới"}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit((data) => onSubmit(data, onClose))} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <input
                                {...register("fullName")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Họ và tên"
                            />
                            {errors.fullName && (
                                <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
                            )}
                        </div>

                        <div>
                            <input
                                {...register("phone")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Số điện thoại"
                            />
                            {errors.phone && (
                                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                            )}
                        </div>
                    </div>

                    <ProvinceSelect
                        value={address}
                        onChange={handleSelectChange}
                        provincesData={provincesData}
                    />

                    <div>
                        <input
                            {...register("addressLine")}
                            className="w-full border px-3 py-2 rounded"
                            placeholder="Địa chỉ cụ thể"
                        />
                        {errors.addressLine && (
                            <p className="text-red-500 text-sm mt-1">{errors.addressLine.message}</p>
                        )}
                    </div>

                    {!isEditing && (
                        <div className="flex items-center gap-2">
                            <Controller
                                control={control}
                                name="isDefault"
                                render={({ field }) => (
                                    <>
                                        <Checkbox
                                            id="isDefault"
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                        <Label htmlFor="isDefault">Đặt làm địa chỉ mặc định</Label>
                                    </>
                                )}
                            />
                        </div>
                    )}

                    <DialogFooter className="mt-6">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Hủy
                        </Button>
                        <Button
                            type="submit"
                            className="bg-red-500 hover:bg-red-600 text-white"
                            disabled={isPending}
                        >
                            {isPending ? "Đang xử lý..." : isEditing ? "Cập nhật" : "Thêm mới"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}