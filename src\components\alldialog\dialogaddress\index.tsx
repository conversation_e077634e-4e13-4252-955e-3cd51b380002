"use client"

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>nt,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import DynamicProvinceSelect from "@/components/dynamic-province-select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import useCreateAddress from "@/app/(user)/address-list/hooks/useCreateAddress"
import { useEffect, useState } from "react"
import { Controller } from "react-hook-form"
import useUpdateAddress from "@/app/(user)/address-list/hooks/useUpdateAddress"
import { convertAddressFromBE } from "@/utils/address"
import useAddressData, { SimpleAddress } from "@/hooks/use-address-data"

export default function AddressDialog({
    open,
    onClose,
    editingAddress,
}: {
    open: boolean
    onClose: () => void
    editingAddress: API.ResponseAddress | null
}) {
    const isEditing = !!editingAddress
    const {
        register,
        handleSubmit,
        control,
        onSubmit,
        watch,
        errors,
        setValue,
        isPending,
        reset,
    } = isEditing
            ? useUpdateAddress({
                addressId: editingAddress.id,
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
            })
            : useCreateAddress()

    const [address, setAddress] = useState<{
        province: SimpleAddress | null,
        district: SimpleAddress | null,
        ward: SimpleAddress | null,
    }>({
        province: null,
        district: null,
        ward: null,
    });

    // Use custom hook for address data
    const {
        provincesData,
        loadingProvinces,
        loadingDistricts,
        loadingWards,
        loadDistricts,
        loadWards
    } = useAddressData();

    useEffect(() => {
        if (!open || provincesData.length === 0) return;

        if (editingAddress) {
            const addressObj = convertAddressFromBE(editingAddress, provincesData);
            setAddress(addressObj);
            reset({
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
                isDefault: editingAddress.isDefault || false,
            });
        } else {
            setAddress({
                province: null,
                district: null,
                ward: null,
            });
            reset({
                fullName: "",
                phone: "",
                addressLine: "",
                province: "",
                city: "",
                postalCode: "",
                isDefault: false,
            });
        }
    }, [open, editingAddress, provincesData, reset]);

    useEffect(() => {
    }, [address]);

    const handleSelectChange = (value: {
        province: SimpleAddress | null
        district: SimpleAddress | null
        ward: SimpleAddress | null
    }) => {
        setAddress(value)
        setValue("province", value.province?.name || "")
        setValue("city", value.district?.name || "")
        setValue("postalCode", value.ward?.code || "")
    }

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-xl max-h-[100vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">
                        {isEditing ? "Cập nhật địa chỉ" : "Thêm địa chỉ mới"}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit((data) => onSubmit(data, onClose))} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <input
                                {...register("fullName")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Họ và tên"
                            />
                            {errors.fullName && (
                                <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
                            )}
                        </div>

                        <div>
                            <input
                                {...register("phone")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Số điện thoại"
                            />
                            {errors.phone && (
                                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                            )}
                        </div>
                    </div>

                    <DynamicProvinceSelect
                        value={address}
                        onChange={handleSelectChange}
                        provincesData={provincesData}
                        loadingProvinces={loadingProvinces}
                        loadingDistricts={loadingDistricts}
                        loadingWards={loadingWards}
                        loadDistricts={loadDistricts}
                        loadWards={loadWards}
                    />

                    <div>
                        <input
                            {...register("addressLine")}
                            className="w-full border px-3 py-2 rounded"
                            placeholder="Địa chỉ cụ thể"
                        />
                        {errors.addressLine && (
                            <p className="text-red-500 text-sm mt-1">{errors.addressLine.message}</p>
                        )}
                    </div>

                    {!isEditing && (
                        <div className="flex items-center gap-2">
                            <Controller
                                control={control}
                                name="isDefault"
                                render={({ field }) => (
                                    <>
                                        <Checkbox
                                            id="isDefault"
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                        <Label htmlFor="isDefault">Đặt làm địa chỉ mặc định</Label>
                                    </>
                                )}
                            />
                        </div>
                    )}

                    <DialogFooter className="mt-6">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Hủy
                        </Button>
                        <Button
                            type="submit"
                            className="bg-red-500 hover:bg-red-600 text-white"
                            disabled={isPending}
                        >
                            {isPending ? "Đang xử lý..." : isEditing ? "Cập nhật" : "Thêm mới"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}