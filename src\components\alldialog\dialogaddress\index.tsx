"use client"

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import ProvinceSelect from "@/components/province-selected"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import useCreateAddress from "@/app/(user)/address-list/hooks/useCreateAddress"
import { useEffect, useState } from "react"
import { Controller } from "react-hook-form"
import useUpdateAddress from "@/app/(user)/address-list/hooks/useUpdateAddress"
import { convertAddressFromBE } from "@/utils/address"
import { getProvinces, getDistricts, getWards } from "@/services/shipping/api-services"

type SimpleAddress = {
    name: string
    code: string
}

type Province = { code: string; name: string; districts: District[] };
type District = { code: string; name: string; wards: Ward[] };
type Ward = { code: string; name: string; level?: string };

export default function AddressDialog({
    open,
    onClose,
    editing<PERSON>ddress,
}: {
    open: boolean
    onClose: () => void
    editingAddress: API.ResponseAddress | null
}) {
    const isEditing = !!editingAddress
    const {
        register,
        handleSubmit,
        control,
        onSubmit,
        watch,
        errors,
        setValue,
        isPending,
        reset,
    } = isEditing
            ? useUpdateAddress({
                addressId: editingAddress.id,
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
            })
            : useCreateAddress()

    const [address, setAddress] = useState<{
        province: SimpleAddress | null,
        district: SimpleAddress | null,
        ward: SimpleAddress | null,
    }>({
        province: null,
        district: null,
        ward: null,
    });

    const [provincesData, setProvincesData] = useState<Province[]>([]);

    useEffect(() => {
        const fetchProvinces = async () => {
            try {
                const provinces = await getProvinces();
                console.log("🔍 Raw provinces from API:", provinces);
                console.log("🔍 First province:", provinces[0]);

                const parsed = provinces.map((p: any) => ({
                    name: p.provinceName,
                    code: p.provinceID.toString(),
                    districts: [] // Will be loaded dynamically when needed
                }));

                console.log("🔍 Parsed provinces:", parsed);
                console.log("🔍 First parsed province:", parsed[0]);
                setProvincesData(parsed);
            } catch (error) {
                console.error("Failed to fetch provinces:", error);
            }
        };

        fetchProvinces();
    }, []);

    useEffect(() => {
        if (!open || provincesData.length === 0) return;

        if (editingAddress) {
            const addressObj = convertAddressFromBE(editingAddress, provincesData);
            setAddress(addressObj);
            reset({
                fullName: editingAddress.fullName,
                phone: editingAddress.phone,
                addressLine: editingAddress.addressLine,
                province: editingAddress.province,
                city: editingAddress.city,
                postalCode: editingAddress.postalCode ?? "",
                isDefault: editingAddress.isDefault || false,
            });
        } else {
            setAddress({
                province: null,
                district: null,
                ward: null,
            });
            reset({
                fullName: "",
                phone: "",
                addressLine: "",
                province: "",
                city: "",
                postalCode: "",
                isDefault: false,
            });
        }
    }, [open, editingAddress, provincesData, reset]);

    useEffect(() => {
    }, [address]);

    // Load districts when province is selected
    const loadDistrictsForProvince = async (provinceCode: string) => {
        try {
            const districts = await getDistricts({ provinceId: parseInt(provinceCode) });
            const parsedDistricts = districts.map((d: any) => ({
                name: d.districtName,
                code: d.districtID.toString(),
                wards: [] // Will be loaded when district is selected
            }));

            setProvincesData(prev =>
                prev.map(p =>
                    p.code === provinceCode
                        ? { ...p, districts: parsedDistricts }
                        : p
                )
            );
        } catch (error) {
            console.error("Failed to load districts:", error);
        }
    };

    // Load wards when district is selected
    const loadWardsForDistrict = async (provinceCode: string, districtCode: string) => {
        try {
            const wards = await getWards({ districtId: parseInt(districtCode) });
            const parsedWards = wards.map((w: any) => ({
                name: w.wardName,
                code: w.wardCode.toString(),
                level: w.level || ""
            }));

            setProvincesData(prev =>
                prev.map(p =>
                    p.code === provinceCode
                        ? {
                            ...p,
                            districts: p.districts.map(d =>
                                d.code === districtCode
                                    ? { ...d, wards: parsedWards }
                                    : d
                            )
                        }
                        : p
                )
            );
        } catch (error) {
            console.error("Failed to load wards:", error);
        }
    };

    const handleSelectChange = async (value: {
        province: SimpleAddress | null
        district: SimpleAddress | null
        ward: SimpleAddress | null
    }) => {
        setAddress(value);
        setValue("province", value.province?.name || "");
        setValue("city", value.district?.name || "");
        setValue("postalCode", value.ward?.code || "");

        // Load districts when province changes
        if (value.province && value.province !== address.province) {
            const province = provincesData.find(p => p.code === value.province?.code);
            if (province && province.districts.length === 0) {
                await loadDistrictsForProvince(value.province.code);
            }
        }

        // Load wards when district changes
        if (value.district && value.district !== address.district && value.province) {
            const province = provincesData.find(p => p.code === value.province?.code);
            const district = province?.districts.find(d => d.code === value.district?.code);
            if (district && district.wards.length === 0) {
                await loadWardsForDistrict(value.province.code, value.district.code);
            }
        }
    };

    return (
        <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
            <DialogContent className="max-w-xl max-h-[100vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="text-xl font-semibold">
                        {isEditing ? "Cập nhật địa chỉ" : "Thêm địa chỉ mới"}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit((data) => onSubmit(data, onClose))} className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <input
                                {...register("fullName")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Họ và tên"
                            />
                            {errors.fullName && (
                                <p className="text-red-500 text-sm mt-1">{errors.fullName.message}</p>
                            )}
                        </div>

                        <div>
                            <input
                                {...register("phone")}
                                className="border px-3 py-2 rounded w-full"
                                placeholder="Số điện thoại"
                            />
                            {errors.phone && (
                                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
                            )}
                        </div>
                    </div>

                    <ProvinceSelect
                        value={address}
                        onChange={handleSelectChange}
                        provincesData={provincesData}
                    />

                    <div>
                        <input
                            {...register("addressLine")}
                            className="w-full border px-3 py-2 rounded"
                            placeholder="Địa chỉ cụ thể"
                        />
                        {errors.addressLine && (
                            <p className="text-red-500 text-sm mt-1">{errors.addressLine.message}</p>
                        )}
                    </div>

                    {!isEditing && (
                        <div className="flex items-center gap-2">
                            <Controller
                                control={control}
                                name="isDefault"
                                render={({ field }) => (
                                    <>
                                        <Checkbox
                                            id="isDefault"
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                        <Label htmlFor="isDefault">Đặt làm địa chỉ mặc định</Label>
                                    </>
                                )}
                            />
                        </div>
                    )}

                    <DialogFooter className="mt-6">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Hủy
                        </Button>
                        <Button
                            type="submit"
                            className="bg-red-500 hover:bg-red-600 text-white"
                            disabled={isPending}
                        >
                            {isPending ? "Đang xử lý..." : isEditing ? "Cập nhật" : "Thêm mới"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}